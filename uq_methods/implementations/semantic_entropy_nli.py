"""
NLI Semantic Entropy method using NLI-based clustering for uncertainty quantification
"""
import numpy as np
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from uq_methods.base import BaseUQMethod
from core.nli_shared import get_nli_calculator

log = logging.getLogger(__name__)


@dataclass
class SemanticCluster:
    """Represents a cluster of semantically equivalent responses."""
    cluster_id: int
    responses: List[str]
    representative: str
    size: int = 0


class SemanticEntropyNLIUQ(BaseUQMethod):
    """
    Semantic Entropy method using NLI-based clustering.

    This implementation follows the approach from "Detecting hallucinations in large language
    models using semantic entropy" paper, which uses NLI to cluster semantically
    equivalent responses and then calculates entropy based on cluster membership.
    """

    def __init__(self,
                 model_name: str = "microsoft/deberta-large-mnli",
                 entailment_threshold: float = 0.5,
                 strict_entailment: bool = True,
                 verbose: bool = False):
        """
        Initialize the semantic entropy calculator.

        Args:
            model_name: NLI model to use for entailment computation
            entailment_threshold: Threshold for considering two responses as semantically equivalent
            strict_entailment: Whether to use strict bidirectional entailment
            verbose: Whether to print debug information
        """
        self.model_name = model_name
        self.entailment_threshold = entailment_threshold
        self.strict_entailment = strict_entailment
        self.verbose = verbose
        self.clusters: List[SemanticCluster] = []

        # Use shared cached NLI calculator
        self.nli_calc = get_nli_calculator(model_name)
    
    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        Compute semantic entropy using NLI-based clustering.

        Args:
            responses: List of generated responses from the LLM

        Returns:
            Dictionary containing uncertainty metrics
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 0.0,
                "semantic_entropy": 0.0,
                "cluster_count": 1,
                "clusters": [{"cluster_id": 0, "responses": responses, "size": len(responses)}],
                "method": "SemanticEntropy_NLI",
                "error": "Need at least 2 responses for meaningful semantic entropy"
            }

        try:
            # Step 1: Cluster responses based on NLI relationships
            clusters = self._cluster_responses(responses)
            self.clusters = clusters

            # Step 2: Calculate entropy based on cluster distribution
            semantic_entropy = self._calculate_cluster_entropy(clusters)

            # Step 3: Calculate additional metrics
            cluster_info = self._get_cluster_info()

            if self.verbose:
                log.debug(f"Generated responses: {responses}")
                log.debug(f"Number of clusters: {len(clusters)}")
                log.debug(f"Semantic entropy: {semantic_entropy}")
                for i, cluster in enumerate(clusters):
                    log.debug(f"Cluster {i}: {len(cluster.responses)} responses")

            return {
                "uncertainty_score": semantic_entropy,
                "semantic_entropy": semantic_entropy,
                "cluster_count": len(clusters),
                "clusters": cluster_info["clusters"],
                "num_responses": len(responses),
                "method": "SemanticEntropy_NLI",
                "metadata": {
                    "method": "SemanticEntropy_NLI",
                    "model_name": self.model_name,
                    "entailment_threshold": self.entailment_threshold,
                    "strict_entailment": self.strict_entailment,
                    "verbose": self.verbose
                }
            }

        except Exception as e:
            log.error(f"Error computing semantic entropy: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "semantic_entropy": 1.0,
                "cluster_count": len(responses),  # Each response in its own cluster
                "clusters": [],
                "error": str(e),
                "method": "SemanticEntropy_NLI"
            }
    
    def _cluster_responses(self, responses: List[str]) -> List[SemanticCluster]:
        """
        Cluster responses based on NLI relationships.

        This method implements the NLI-based clustering approach where:
        - Two responses are in the same cluster if they have high mutual entailment
        - Uses the shared NLI calculator for consistency

        Args:
            responses: List of responses to cluster

        Returns:
            List of SemanticCluster objects
        """
        if not responses:
            return []

        n = len(responses)
        clusters = []
        assigned = set()

        # Build similarity matrix based on NLI
        similarity_matrix = self._build_nli_matrix(responses)

        # Group responses based on NLI relationships
        cluster_id = 0
        for i in range(n):
            if i in assigned:
                continue

            # Start new cluster with this response
            cluster_responses = [responses[i]]
            assigned.add(i)

            # Find all responses that are semantically equivalent
            for j in range(n):
                if j in assigned or j == i:
                    continue

                # Check if responses i and j are semantically equivalent
                if self._are_semantically_equivalent(
                    similarity_matrix[i][j],
                    similarity_matrix[j][i]
                ):
                    cluster_responses.append(responses[j])
                    assigned.add(j)

            # Create cluster
            cluster = SemanticCluster(
                cluster_id=cluster_id,
                responses=cluster_responses,
                representative=cluster_responses[0],
                size=len(cluster_responses)
            )
            clusters.append(cluster)
            cluster_id += 1

        return clusters
    
    def _build_nli_matrix(self, responses: List[str]) -> np.ndarray:
        """
        Build NLI similarity matrix for responses.

        Args:
            responses: List of response texts

        Returns:
            NLI similarity matrix where entry (i,j) is entailment score from response i to response j
        """
        n = len(responses)
        similarity_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    # Use cached NLI calculator
                    nli_result = self.nli_calc.compute_nli_scores_cached(responses[i], responses[j])
                    similarity_matrix[i, j] = nli_result.entailment

        return similarity_matrix

    def _are_semantically_equivalent(self, score_ij: float, score_ji: float) -> bool:
        """
        Check if two responses are semantically equivalent based on NLI scores.

        Args:
            score_ij: Entailment score from response i to response j
            score_ji: Entailment score from response j to response i

        Returns:
            True if responses are semantically equivalent
        """
        if self.strict_entailment:
            # Both directions must exceed threshold (bidirectional entailment)
            return score_ij >= self.entailment_threshold and score_ji >= self.entailment_threshold
        else:
            # At least one direction must exceed threshold
            return max(score_ij, score_ji) >= self.entailment_threshold
    
    def _calculate_cluster_entropy(self, clusters: List[SemanticCluster]) -> float:
        """
        Calculate entropy based on cluster distribution.

        Args:
            clusters: List of semantic clusters

        Returns:
            Normalized semantic entropy [0, 1]
        """
        if not clusters:
            return 0.0

        if len(clusters) == 1:
            return 0.0

        # Calculate cluster probabilities
        total_responses = sum(cluster.size for cluster in clusters)
        cluster_probs = [cluster.size / total_responses for cluster in clusters]

        # Calculate entropy
        entropy = -sum(p * np.log(p) for p in cluster_probs if p > 0)

        # Normalize by maximum possible entropy
        max_entropy = np.log(len(clusters))
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.0

        return normalized_entropy

    def _get_cluster_info(self) -> Dict[str, Any]:
        """
        Get detailed information about clusters.

        Returns:
            Dictionary with cluster information
        """
        cluster_info = {
            "clusters": [],
            "total_clusters": len(self.clusters),
            "total_responses": sum(cluster.size for cluster in self.clusters)
        }

        for cluster in self.clusters:
            cluster_info["clusters"].append({
                "cluster_id": cluster.cluster_id,
                "size": cluster.size,
                "representative": cluster.representative,
                "responses": cluster.responses
            })

        return cluster_info

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 5

    def get_method_name(self) -> str:
        """Get method name."""
        return "SemanticEntropy_NLI"