"""
Semantic Entropy implementation following the reference code structure.
Based on "Detecting hallucinations in large language models using semantic entropy" paper.
"""
import numpy as np
import logging
from typing import List, Dict, Any
from collections import defaultdict
from uq_methods.base import BaseUQMethod
from core.nli_shared import get_nli_calculator

log = logging.getLogger(__name__)


class SemanticEntropyReferenceUQ(BaseUQMethod):
    """
    Semantic Entropy implementation following the reference code structure.
    
    This implementation closely follows the approach from the reference code you provided,
    using NLI-based clustering and entropy calculation without logits/logprobs.
    """
    
    def __init__(self, 
                 model_name: str = "microsoft/deberta-large-mnli",
                 strict_entailment: bool = True,
                 verbose: bool = False):
        """
        Initialize the semantic entropy calculator.
        
        Args:
            model_name: NLI model to use for entailment computation
            strict_entailment: Whether to use strict bidirectional entailment
            verbose: Whether to print debug information
        """
        self.model_name = model_name
        self.strict_entailment = strict_entailment
        self.verbose = verbose
        
        # Use shared cached NLI calculator (similar to EntailmentDeberta in reference)
        self.nli_calc = get_nli_calculator(model_name)
    
    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        Compute uncertainty using semantic entropy following reference implementation.
        
        Args:
            responses: List of generated responses from the LLM
            
        Returns:
            Dictionary containing uncertainty metrics
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 0.0,
                "semantic_entropy": 0.0,
                "cluster_assignment_entropy": 0.0,
                "semantic_ids": list(range(len(responses))),
                "method": "SemanticEntropy_Reference",
                "error": "Need at least 2 responses"
            }
        
        try:
            # Step 1: Get semantic IDs (equivalent to get_semantic_ids in reference)
            semantic_ids = self._get_semantic_ids(responses)
            
            # Step 2: Calculate cluster assignment entropy
            cluster_assignment_entropy = self._cluster_assignment_entropy(semantic_ids)
            
            # Step 3: Calculate semantic entropy (following predictive_entropy_rao pattern)
            # Since we don't have log_likelihoods, we use uniform probabilities within clusters
            semantic_entropy = self._calculate_semantic_entropy(responses, semantic_ids)
            
            if self.verbose:
                log.debug(f"Responses: {responses}")
                log.debug(f"Semantic IDs: {semantic_ids}")
                log.debug(f"Cluster assignment entropy: {cluster_assignment_entropy}")
                log.debug(f"Semantic entropy: {semantic_entropy}")
            
            return {
                "uncertainty_score": semantic_entropy,
                "semantic_entropy": semantic_entropy,
                "cluster_assignment_entropy": cluster_assignment_entropy,
                "semantic_ids": semantic_ids,
                "num_clusters": len(set(semantic_ids)),
                "num_responses": len(responses),
                "method": "SemanticEntropy_Reference",
                "metadata": {
                    "method": "SemanticEntropy_Reference",
                    "model_name": self.model_name,
                    "strict_entailment": self.strict_entailment,
                    "verbose": self.verbose
                }
            }
            
        except Exception as e:
            log.error(f"Error computing semantic entropy: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "semantic_entropy": 1.0,
                "cluster_assignment_entropy": 1.0,
                "semantic_ids": list(range(len(responses))),
                "error": str(e),
                "method": "SemanticEntropy_Reference"
            }
    
    def _get_semantic_ids(self, responses: List[str]) -> List[int]:
        """
        Get semantic IDs for responses (equivalent to get_semantic_ids in reference).
        
        This function clusters responses based on NLI relationships and assigns
        cluster IDs to each response.
        
        Args:
            responses: List of response texts
            
        Returns:
            List of semantic cluster IDs for each response
        """
        n = len(responses)
        semantic_ids = list(range(n))  # Initialize each response in its own cluster
        
        if n < 2:
            return semantic_ids
        
        # Build entailment matrix
        entailment_matrix = self._build_entailment_matrix(responses)
        
        # Merge clusters based on entailment relationships
        for i in range(n):
            for j in range(i + 1, n):
                if self._context_entails_response(responses[i], responses[j], entailment_matrix[i][j], entailment_matrix[j][i]):
                    # Merge clusters: assign all responses with semantic_ids[j] to semantic_ids[i]
                    old_id = semantic_ids[j]
                    new_id = semantic_ids[i]
                    for k in range(n):
                        if semantic_ids[k] == old_id:
                            semantic_ids[k] = new_id
        
        # Renumber clusters to be consecutive starting from 0
        unique_ids = sorted(set(semantic_ids))
        id_mapping = {old_id: new_id for new_id, old_id in enumerate(unique_ids)}
        semantic_ids = [id_mapping[sid] for sid in semantic_ids]
        
        return semantic_ids
    
    def _build_entailment_matrix(self, responses: List[str]) -> np.ndarray:
        """Build entailment matrix using NLI model."""
        n = len(responses)
        entailment_matrix = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    entailment_matrix[i, j] = 1.0
                else:
                    nli_result = self.nli_calc.compute_nli_scores_cached(responses[i], responses[j])
                    entailment_matrix[i, j] = nli_result.entailment
        
        return entailment_matrix
    
    def _context_entails_response(self, response1: str, response2: str, 
                                  entail_12: float, entail_21: float) -> bool:
        """
        Check if two responses are semantically equivalent (similar to context_entails_response).
        
        Args:
            response1: First response
            response2: Second response  
            entail_12: Entailment score from response1 to response2
            entail_21: Entailment score from response2 to response1
            
        Returns:
            True if responses should be in the same semantic cluster
        """
        if self.strict_entailment:
            # Both directions must have high entailment (bidirectional)
            return entail_12 > 0.5 and entail_21 > 0.5
        else:
            # At least one direction must have high entailment
            return max(entail_12, entail_21) > 0.5
    
    def _cluster_assignment_entropy(self, semantic_ids: List[int]) -> float:
        """
        Calculate cluster assignment entropy (equivalent to cluster_assignment_entropy in reference).
        
        Args:
            semantic_ids: List of cluster IDs for each response
            
        Returns:
            Cluster assignment entropy
        """
        if not semantic_ids:
            return 0.0
        
        # Count frequency of each cluster
        cluster_counts = defaultdict(int)
        for sid in semantic_ids:
            cluster_counts[sid] += 1
        
        # Calculate probabilities
        total = len(semantic_ids)
        cluster_probs = [count / total for count in cluster_counts.values()]
        
        # Calculate entropy
        entropy = -sum(p * np.log(p) for p in cluster_probs if p > 0)
        
        return entropy
    
    def _calculate_semantic_entropy(self, responses: List[str], semantic_ids: List[int]) -> float:
        """
        Calculate semantic entropy following the reference implementation pattern.
        
        Since we don't have log_likelihoods, we assume uniform probability within each cluster
        and calculate entropy based on cluster sizes.
        
        Args:
            responses: List of response texts
            semantic_ids: List of cluster IDs
            
        Returns:
            Semantic entropy score
        """
        if len(set(semantic_ids)) == 1:
            return 0.0  # All responses in same cluster
        
        # Count responses per cluster
        cluster_counts = defaultdict(int)
        for sid in semantic_ids:
            cluster_counts[sid] += 1
        
        # Calculate cluster probabilities (uniform within cluster assumption)
        total_responses = len(responses)
        cluster_probs = [count / total_responses for count in cluster_counts.values()]
        
        # Calculate entropy (equivalent to predictive_entropy_rao with uniform log_likelihoods)
        entropy = -sum(p * np.log(p) for p in cluster_probs if p > 0)
        
        return entropy
    
    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 5
    
    def get_method_name(self) -> str:
        """Get method name."""
        return "SemanticEntropy_Reference"
