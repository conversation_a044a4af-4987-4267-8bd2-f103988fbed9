# Semantic Entropy Refactoring Documentation

## Overview

This document describes the refactoring of your semantic entropy implementation to align with the reference code structure while integrating with your new NLI model (`eccentricity_nli_entail.py`).

## Key Changes Made

### 1. **Architecture Alignment**
- **Before**: Async-based method with Qwen-specific NLI reasoning
- **After**: Synchronous method following the reference code pattern
- **Benefit**: Consistent with your existing UQ method architecture and the reference implementation

### 2. **NLI Integration**
- **Integration**: Uses your shared NLI calculator (`core.nli_shared.get_nli_calculator`)
- **Caching**: Leverages the caching system from your `eccentricity_nli_entail.py`
- **Consistency**: Same NLI model and scoring across all methods

### 3. **Text-Based Focus**
- **Removed**: Dependency on logits and logprobs (as requested)
- **Focus**: Pure text-based semantic clustering using NLI scores
- **Approach**: Follows the reference code's `get_semantic_ids` and clustering pattern

## Implementation Files

### 1. `semantic_entropy_nli.py` (Main Implementation)
```python
class SemanticEntropyNLIUQ(BaseUQMethod):
    """
    Semantic Entropy method using NLI-based clustering.
    
    This implementation follows the approach from "Detecting hallucinations in large language 
    models using semantic entropy" paper, which uses NLI to cluster semantically 
    equivalent responses and then calculates entropy based on cluster membership.
    """
```

**Key Features:**
- NLI-based response clustering
- Configurable entailment thresholds
- Strict vs. relaxed entailment modes
- Comprehensive cluster analysis

### 2. `semantic_entropy_reference.py` (Reference-Style Implementation)
```python
class SemanticEntropyReferenceUQ(BaseUQMethod):
    """
    Semantic Entropy implementation following the reference code structure.
    
    This implementation closely follows the approach from the reference code you provided,
    using NLI-based clustering and entropy calculation without logits/logprobs.
    """
```

**Key Features:**
- Mirrors reference code functions (`get_semantic_ids`, `cluster_assignment_entropy`)
- Compatible with reference code patterns
- Uniform probability assumption within clusters

## Core Algorithm

### Step 1: NLI Matrix Construction
```python
def _build_nli_matrix(self, responses: List[str]) -> np.ndarray:
    """Build NLI similarity matrix for responses."""
    for i in range(n):
        for j in range(n):
            nli_result = self.nli_calc.compute_nli_scores_cached(responses[i], responses[j])
            similarity_matrix[i, j] = nli_result.entailment
```

### Step 2: Semantic Clustering
```python
def _are_semantically_equivalent(self, score_ij: float, score_ji: float) -> bool:
    """Check if two responses are semantically equivalent."""
    if self.strict_entailment:
        # Both directions must exceed threshold (bidirectional entailment)
        return score_ij >= self.entailment_threshold and score_ji >= self.entailment_threshold
    else:
        # At least one direction must exceed threshold
        return max(score_ij, score_ji) >= self.entailment_threshold
```

### Step 3: Entropy Calculation
```python
def _calculate_cluster_entropy(self, clusters: List[SemanticCluster]) -> float:
    """Calculate entropy based on cluster distribution."""
    cluster_probs = [cluster.size / total_responses for cluster in clusters]
    entropy = -sum(p * np.log(p) for p in cluster_probs if p > 0)
    normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.0
```

## Configuration Options

### Entailment Threshold
- **Default**: 0.5
- **Purpose**: Determines when two responses are considered semantically equivalent
- **Impact**: Lower values create larger clusters (less uncertainty), higher values create smaller clusters (more uncertainty)

### Strict Entailment Mode
- **Default**: True
- **Strict Mode**: Requires bidirectional entailment (A→B AND B→A)
- **Relaxed Mode**: Requires unidirectional entailment (A→B OR B→A)

### NLI Model
- **Default**: "microsoft/deberta-large-mnli"
- **Customizable**: Can use any NLI model supported by your system
- **Cached**: Uses your existing caching infrastructure

## Usage Examples

### Basic Usage
```python
from uq_methods.implementations.semantic_entropy_nli import SemanticEntropyNLIUQ

# Initialize method
method = SemanticEntropyNLIUQ(
    model_name="microsoft/deberta-large-mnli",
    entailment_threshold=0.5,
    strict_entailment=True,
    verbose=True
)

# Compute uncertainty
responses = [
    "The capital of France is Paris.",
    "Paris is the capital city of France.",
    "The capital of Germany is Berlin."
]

results = method.compute_uncertainty(responses)
print(f"Semantic Entropy: {results['semantic_entropy']:.4f}")
print(f"Number of Clusters: {results['cluster_count']}")
```

### Reference-Style Usage
```python
from uq_methods.implementations.semantic_entropy_reference import SemanticEntropyReferenceUQ

# Initialize reference method
ref_method = SemanticEntropyReferenceUQ(
    model_name="microsoft/deberta-large-mnli",
    strict_entailment=True,
    verbose=True
)

results = ref_method.compute_uncertainty(responses)
print(f"Semantic IDs: {results['semantic_ids']}")
print(f"Cluster Assignment Entropy: {results['cluster_assignment_entropy']:.4f}")
```

## Integration with Your Existing Code

### 1. **NLI Calculator Integration**
The refactored code uses your existing `get_nli_calculator` function:
```python
from core.nli_shared import get_nli_calculator
self.nli_calc = get_nli_calculator(model_name)
```

### 2. **Base Class Compatibility**
Follows your `BaseUQMethod` interface:
```python
class SemanticEntropyNLIUQ(BaseUQMethod):
    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
    def get_required_samples(self) -> int:
    def get_method_name(self) -> str:
```

### 3. **Consistent Output Format**
Returns standardized uncertainty metrics:
```python
{
    "uncertainty_score": float,
    "semantic_entropy": float,
    "cluster_count": int,
    "clusters": List[Dict],
    "method": str,
    "metadata": Dict
}
```

## Testing

Run the test script to verify the implementation:
```bash
python test_semantic_entropy.py
```

This will test both implementations with various response patterns:
- Identical responses (low entropy)
- Semantically similar responses (medium entropy)
- Mixed semantic responses (high entropy)
- Completely different responses (maximum entropy)

## Benefits of the Refactoring

1. **Consistency**: Aligns with your existing UQ method architecture
2. **Integration**: Uses your shared NLI infrastructure and caching
3. **Flexibility**: Configurable entailment thresholds and modes
4. **Reference Compatibility**: Includes reference-style implementation
5. **Text-Focused**: No dependency on logits/logprobs as requested
6. **Comprehensive**: Detailed cluster analysis and metrics
7. **Robust**: Error handling and edge case management
8. **Documented**: Clear documentation and examples

## Next Steps

1. **Test**: Run the test script to verify functionality
2. **Integrate**: Add to your main UQ method registry
3. **Tune**: Adjust entailment thresholds based on your data
4. **Extend**: Consider adding additional clustering algorithms if needed
5. **Optimize**: Profile performance with your typical response sizes
