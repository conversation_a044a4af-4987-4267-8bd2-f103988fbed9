#!/usr/bin/env python3
"""
Test script for the refactored semantic entropy implementations.
"""
import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from uq_methods.implementations.semantic_entropy import SemanticEntropyNLIUQ

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def test_semantic_entropy_methods():
    """Test both semantic entropy implementations."""
    
    # Test responses with different levels of semantic similarity
    test_cases = [
        {
            "name": "Identical responses",
            "responses": [
                "The capital of France is Paris.",
                "The capital of France is Paris.",
                "The capital of France is Paris."
            ]
        },
        {
            "name": "Semantically similar responses",
            "responses": [
                "The capital of France is Paris.",
                "Paris is the capital city of France.",
                "France's capital is Paris.",
                "The French capital is Paris."
            ]
        },
        {
            "name": "Mixed semantic responses",
            "responses": [
                "The capital of France is Paris.",
                "Paris is the capital city of France.",
                "The capital of Germany is Berlin.",
                "Berlin is Germany's capital city.",
                "The weather is nice today."
            ]
        },
        {
            "name": "Completely different responses",
            "responses": [
                "The capital of France is Paris.",
                "I like to eat pizza.",
                "The weather is sunny today.",
                "Machine learning is fascinating.",
                "The ocean is blue."
            ]
        }
    ]
    
    # Initialize semantic entropy method
    semantic_entropy_method = SemanticEntropyNLIUQ(verbose=True)

    print("=" * 80)
    print("SEMANTIC ENTROPY TEST")
    print("=" * 80)
    
    for test_case in test_cases:
        print(f"\n{'='*60}")
        print(f"Test Case: {test_case['name']}")
        print(f"{'='*60}")
        
        responses = test_case['responses']
        print(f"Number of responses: {len(responses)}")
        print("Responses:")
        for i, response in enumerate(responses):
            print(f"  {i+1}. {response}")
        
        print(f"\n{'-'*40}")
        print("Semantic Entropy Results:")
        print(f"{'-'*40}")

        try:
            results = semantic_entropy_method.compute_uncertainty(responses)
            print(f"Uncertainty Score: {results['uncertainty_score']:.4f}")
            print(f"Semantic Entropy: {results['semantic_entropy']:.4f}")
            print(f"Number of Clusters: {results['cluster_count']}")

            if 'clusters' in results and results['clusters']:
                print("Cluster Details:")
                for cluster in results['clusters']:
                    print(f"  Cluster {cluster['cluster_id']}: {cluster['size']} responses")
                    print("    Responses in this cluster:")
                    for i, response in enumerate(cluster['responses']):
                        print(f"      {i+1}. {response}")

            if 'error' in results:
                print(f"Error: {results['error']}")

        except Exception as e:
            print(f"Error in semantic entropy method: {str(e)}")
    
    print(f"\n{'='*80}")
    print("TEST COMPLETED")
    print(f"{'='*80}")


def test_single_case():
    """Test a single case for debugging."""
    responses = [
        "The capital of France is Paris.",
        "Paris is the capital city of France.",
        "The capital of Germany is Berlin."
    ]

    print("Testing single case for debugging...")
    print(f"Responses: {responses}")

    # Test semantic entropy method
    method = SemanticEntropyNLIUQ(verbose=True)
    results = method.compute_uncertainty(responses)

    print(f"Results: {results}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "single":
        test_single_case()
    else:
        test_semantic_entropy_methods()
